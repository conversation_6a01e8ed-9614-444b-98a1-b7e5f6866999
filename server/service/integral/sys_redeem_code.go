package integral

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/config"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
	integralReq "github.com/flipped-aurora/gin-vue-admin/server/model/integral/request"
	integralRes "github.com/flipped-aurora/gin-vue-admin/server/model/integral/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type RedeemCodeService struct{}

var RedeemCodeServiceApp = new(RedeemCodeService)

// generateCode 生成兑换码
func (s *RedeemCodeService) generateCode(length int) (string, error) {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	code := make([]byte, length)
	for i := range code {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", err
		}
		code[i] = charset[num.Int64()]
	}
	return string(code), nil
}

// generateUniqueCode 生成唯一兑换码
func (s *RedeemCodeService) generateUniqueCode() (string, error) {
	maxAttempts := 10
	for i := 0; i < maxAttempts; i++ {
		code, err := s.generateCode(12) // 生成12位兑换码
		if err != nil {
			return "", err
		}

		// 检查是否已存在
		var count int64
		err = global.GVA_DB.Model(&integral.SysRedeemCode{}).Where("code = ?", code).Count(&count).Error
		if err != nil {
			return "", err
		}

		if count == 0 {
			return code, nil
		}
	}
	return "", fmt.Errorf("生成唯一兑换码失败，请重试")
}

// CreateRedeemCode 创建兑换码
func (s *RedeemCodeService) CreateRedeemCode(req integralReq.CreateRedeemCodeRequest, createdBy uint) (integral.SysRedeemCode, error) {
	code, err := s.generateUniqueCode()
	if err != nil {
		return integral.SysRedeemCode{}, err
	}

	redeemCode := integral.SysRedeemCode{
		Code:        code,
		Type:        req.Type,
		Title:       req.Title,
		Description: req.Description,
		MaxUses:     req.MaxUses,
		ExpiresAt:   req.ExpiresAt,
		IsActive:    true,
		CreatedBy:   createdBy,
		BatchID:     uuid.New().String(),
		Remark:      req.Remark,
	}

	// 设置奖励数据
	if err := redeemCode.SetRewardData(req.RewardData); err != nil {
		return integral.SysRedeemCode{}, fmt.Errorf("设置奖励数据失败: %v", err)
	}

	err = global.GVA_DB.Create(&redeemCode).Error
	return redeemCode, err
}

// BatchCreateRedeemCode 批量创建兑换码
func (s *RedeemCodeService) BatchCreateRedeemCode(req integralReq.BatchCreateRedeemCodeRequest, createdBy uint) (integralRes.BatchCreateRedeemCodeResponse, error) {
	batchID := uuid.New().String()
	var codes []string
	var redeemCodes []integral.SysRedeemCode

	// 批量生成兑换码
	for i := 0; i < req.Count; i++ {
		code, err := s.generateUniqueCode()
		if err != nil {
			return integralRes.BatchCreateRedeemCodeResponse{}, fmt.Errorf("生成第%d个兑换码失败: %v", i+1, err)
		}

		redeemCode := integral.SysRedeemCode{
			Code:        code,
			Type:        req.Type,
			Title:       req.Title,
			Description: req.Description,
			MaxUses:     req.MaxUses,
			ExpiresAt:   req.ExpiresAt,
			IsActive:    true,
			CreatedBy:   createdBy,
			BatchID:     batchID,
			Remark:      req.Remark,
		}

		// 设置奖励数据
		if err := redeemCode.SetRewardData(req.RewardData); err != nil {
			return integralRes.BatchCreateRedeemCodeResponse{}, fmt.Errorf("设置第%d个兑换码奖励数据失败: %v", i+1, err)
		}

		codes = append(codes, code)
		redeemCodes = append(redeemCodes, redeemCode)
	}

	// 批量插入数据库
	err := global.GVA_DB.CreateInBatches(redeemCodes, 100).Error
	if err != nil {
		return integralRes.BatchCreateRedeemCodeResponse{}, err
	}

	return integralRes.BatchCreateRedeemCodeResponse{
		BatchID:     batchID,
		Count:       req.Count,
		Codes:       codes,
		Type:        req.Type,
		Title:       req.Title,
		RewardData:  req.RewardData,
		Description: req.Description,
		ExpiresAt:   req.ExpiresAt,
	}, nil
}

// GetRedeemCodeList 获取兑换码列表
func (s *RedeemCodeService) GetRedeemCodeList(req integralReq.RedeemCodeSearch) (integralRes.RedeemCodeListResponse, error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	db := global.GVA_DB.Model(&integral.SysRedeemCode{})

	// 添加查询条件
	if req.Code != "" {
		db = db.Where("code LIKE ?", "%"+req.Code+"%")
	}
	if req.Type != "" {
		db = db.Where("type = ?", req.Type)
	}
	if req.BatchID != "" {
		db = db.Where("batch_id = ?", req.BatchID)
	}
	if req.IsActive != nil {
		db = db.Where("is_active = ?", *req.IsActive)
	}
	if req.CreatedBy != 0 {
		db = db.Where("created_by = ?", req.CreatedBy)
	}
	if req.StartDate != "" {
		db = db.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		db = db.Where("created_at <= ?", req.EndDate+" 23:59:59")
	}

	// 状态过滤
	if req.Status != "" {
		switch req.Status {
		case integral.RedeemCodeStatusActive:
			db = db.Where("is_active = ? AND (expires_at IS NULL OR expires_at > ?) AND (max_uses = 0 OR used_count < max_uses)", true, time.Now())
		case integral.RedeemCodeStatusInactive:
			db = db.Where("is_active = ?", false)
		case integral.RedeemCodeStatusExpired:
			db = db.Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now())
		case integral.RedeemCodeStatusExhausted:
			db = db.Where("max_uses > 0 AND used_count >= max_uses")
		}
	}

	// 获取总数
	var total int64
	err := db.Count(&total).Error
	if err != nil {
		return integralRes.RedeemCodeListResponse{}, err
	}

	// 获取列表
	var redeemCodes []integral.SysRedeemCode
	err = db.Order("created_at DESC").Limit(limit).Offset(offset).Find(&redeemCodes).Error
	if err != nil {
		return integralRes.RedeemCodeListResponse{}, err
	}

	// 获取创建者信息
	var createdByIDs []uint
	for _, code := range redeemCodes {
		createdByIDs = append(createdByIDs, code.CreatedBy)
	}

	var users []system.SysUser
	userMap := make(map[uint]string)
	if len(createdByIDs) > 0 {
		global.GVA_DB.Where("id IN ?", createdByIDs).Find(&users)
		for _, user := range users {
			userMap[user.ID] = user.NickName
		}
	}

	// 转换为响应格式
	var list []integralRes.RedeemCodeResponse
	for _, code := range redeemCodes {
		createdByName := userMap[code.CreatedBy]
		if createdByName == "" {
			createdByName = "未知"
		}
		list = append(list, integralRes.ConvertToRedeemCodeResponse(code, createdByName))
	}

	return integralRes.RedeemCodeListResponse{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// UpdateRedeemCode 更新兑换码
func (s *RedeemCodeService) UpdateRedeemCode(req integralReq.UpdateRedeemCodeRequest) error {
	// 先获取原记录
	var redeemCode integral.SysRedeemCode
	if err := global.GVA_DB.First(&redeemCode, req.ID).Error; err != nil {
		return fmt.Errorf("兑换码不存在: %v", err)
	}

	// 设置奖励数据
	if err := redeemCode.SetRewardData(req.RewardData); err != nil {
		return fmt.Errorf("设置奖励数据失败: %v", err)
	}

	updates := map[string]interface{}{
		"type":        req.Type,
		"title":       req.Title,
		"description": req.Description,
		"reward_data": redeemCode.RewardData,
		"max_uses":    req.MaxUses,
		"expires_at":  req.ExpiresAt,
		"is_active":   req.IsActive,
		"remark":      req.Remark,
	}

	return global.GVA_DB.Model(&integral.SysRedeemCode{}).Where("id = ?", req.ID).Updates(updates).Error
}

// DeleteRedeemCode 删除兑换码
func (s *RedeemCodeService) DeleteRedeemCode(id uint) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 删除使用记录
		if err := tx.Where("code_id = ?", id).Delete(&integral.SysRedeemCodeUsage{}).Error; err != nil {
			return err
		}
		// 删除兑换码
		return tx.Delete(&integral.SysRedeemCode{}, id).Error
	})
}

// DeleteRedeemCodeBatch 批量删除兑换码
func (s *RedeemCodeService) DeleteRedeemCodeBatch(batchID string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 获取批次中的所有兑换码ID
		var codeIDs []uint
		if err := tx.Model(&integral.SysRedeemCode{}).Where("batch_id = ?", batchID).Pluck("id", &codeIDs).Error; err != nil {
			return err
		}

		if len(codeIDs) > 0 {
			// 删除使用记录
			if err := tx.Where("code_id IN ?", codeIDs).Delete(&integral.SysRedeemCodeUsage{}).Error; err != nil {
				return err
			}
		}

		// 删除兑换码
		return tx.Where("batch_id = ?", batchID).Delete(&integral.SysRedeemCode{}).Error
	})
}

// UseRedeemCode 使用兑换码
func (s *RedeemCodeService) UseRedeemCode(req integralReq.UseRedeemCodeRequest, userID uint, ipAddress, userAgent string) (integralRes.UseRedeemCodeResponse, error) {
	var response integralRes.UseRedeemCodeResponse

	return response, global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 查找兑换码
		var redeemCode integral.SysRedeemCode
		if err := tx.Where("code = ?", strings.ToUpper(req.Code)).First(&redeemCode).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("兑换码不存在")
			}
			return fmt.Errorf("查询兑换码失败: %v", err)
		}

		// 检查兑换码是否可用
		if !redeemCode.CanUse() {
			if !redeemCode.IsActive {
				return fmt.Errorf("兑换码已被禁用")
			}
			if redeemCode.IsExpired() {
				return fmt.Errorf("兑换码已过期")
			}
			if redeemCode.IsExhausted() {
				return fmt.Errorf("兑换码使用次数已达上限")
			}
		}

		// 检查用户是否已经使用过此兑换码（如果是单次使用）
		if redeemCode.MaxUses == 1 {
			var count int64
			if err := tx.Model(&integral.SysRedeemCodeUsage{}).Where("code_id = ? AND user_id = ?", redeemCode.ID, userID).Count(&count).Error; err != nil {
				return fmt.Errorf("检查使用记录失败: %v", err)
			}
			if count > 0 {
				return fmt.Errorf("您已经使用过此兑换码")
			}
		}

		// 获取奖励数据
		rewardData, err := redeemCode.GetRewardData()
		if err != nil {
			return fmt.Errorf("获取奖励数据失败: %v", err)
		}

		// 根据兑换码类型处理奖励
		if err := s.processReward(tx, userID, redeemCode.Type, *rewardData, redeemCode.Title); err != nil {
			return fmt.Errorf("处理奖励失败: %v", err)
		}

		// 更新兑换码使用次数
		if err := tx.Model(&redeemCode).UpdateColumn("used_count", gorm.Expr("used_count + 1")).Error; err != nil {
			return fmt.Errorf("更新兑换码使用次数失败: %v", err)
		}

		// 记录使用记录
		usage := integral.SysRedeemCodeUsage{
			CodeID:     redeemCode.ID,
			UserID:     userID,
			RewardData: redeemCode.RewardData,
			UsedAt:     time.Now(),
			IPAddress:  ipAddress,
			UserAgent:  userAgent,
		}
		if err := tx.Create(&usage).Error; err != nil {
			return fmt.Errorf("记录使用记录失败: %v", err)
		}

		// 设置响应
		response = integralRes.UseRedeemCodeResponse{
			Type:        redeemCode.Type,
			Title:       redeemCode.Title,
			RewardData:  *rewardData,
			Code:        redeemCode.Code,
			Description: redeemCode.Description,
			Message:     s.getRewardMessage(redeemCode.Type, *rewardData),
		}

		return nil
	})
}

// processReward 处理奖励
func (s *RedeemCodeService) processReward(tx *gorm.DB, userID uint, rewardType string, rewardData integral.RewardData, title string) error {
	switch rewardType {
	case integral.RedeemCodeTypePoints:
		// 处理积分奖励
		if rewardData.Points > 0 {
			taskService := TaskServiceApp
			taskID := uint(config.TASK_REDEEM_CODE)
			reason := fmt.Sprintf("使用兑换码获得积分：%s", title)

			return taskService.AddPointsWithTaskRecord(userID, rewardData.Points, reason, "redeem", taskID)
		}

	case integral.RedeemCodeTypeMembership:
		// 处理会员奖励 - 这里需要根据实际的会员系统实现
		// TODO: 实现会员兑换逻辑
		return fmt.Errorf("会员兑换功能暂未实现")

	case integral.RedeemCodeTypeVIP:
		// 处理VIP奖励 - 这里需要根据实际的VIP系统实现
		// TODO: 实现VIP兑换逻辑
		return fmt.Errorf("VIP兑换功能暂未实现")

	case integral.RedeemCodeTypeCustom:
		// 处理自定义奖励 - 可以根据需要扩展
		return fmt.Errorf("自定义兑换功能暂未实现")

	default:
		return fmt.Errorf("不支持的兑换类型: %s", rewardType)
	}

	return nil
}

// getRewardMessage 获取奖励消息
func (s *RedeemCodeService) getRewardMessage(rewardType string, rewardData integral.RewardData) string {
	switch rewardType {
	case integral.RedeemCodeTypePoints:
		return fmt.Sprintf("成功兑换%d积分", rewardData.Points)
	case integral.RedeemCodeTypeMembership:
		return "成功兑换会员"
	case integral.RedeemCodeTypeVIP:
		return fmt.Sprintf("成功兑换VIP%d级，有效期%d天", rewardData.VIPLevel, rewardData.VIPDays)
	case integral.RedeemCodeTypeCustom:
		return "成功兑换自定义奖励"
	default:
		return "兑换成功"
	}
}
